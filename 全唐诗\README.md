全唐诗 & 全宋诗
===============

> **注意:** 《全唐诗》和《全宋诗》都是诗题材的集合， 意味着在本目录找不到词。 宋词请移步到[/ci](https://github.com/chinese-poetry/chinese-poetry/tree/master/ci).


《全唐诗》是清康熙四十四年（1705年），彭定求、沈三曾、杨中讷、汪士鋐、汪绎、俞梅、徐树本、车鼎晋、潘从律、查嗣瑮10人奉敕编校，“得诗四万八千九百余首，凡二千二百余人”， 共计900卷，目录12卷。 来自[百科](https://baike.baidu.com/item/%E5%85%A8%E5%94%90%E8%AF%97)

《全宋诗》继唐诗的高度繁荣之后，宋诗在思想内容和艺术表现上有新的开拓和创造，出现了许多优秀作家作品，形成了许多流派，对元、明、清的诗歌发展产生了深远影响。来自[百科](https://baike.baidu.com/item/%E5%85%A8%E5%AE%8B%E8%AF%97)


## 说明

《全唐诗》和《全宋诗》是繁体存储， 如有需要请自己转换， 但转换后的字不符合上下文。

目前此诗集还有大量错误需要更改， 欢迎提交 PR 修改。 个人精力有限， 但仍有愚公移山精神.

## 数据形式

*poet.tang.[0-57000].json*

*poet.song.[0-254000].json*

每个 JSON 文件有1000条诗.

```text
[
  {
    "author": "太宗皇帝",
    "paragraphs": [
      "秦川雄帝宅，函谷壯皇居。",
      "綺殿千尋起，離宮百雉餘。",
      "連甍遙接漢，飛觀迥凌虛。",
      "雲日隱層闕，風煙出綺疎。"
    ],
    "note": [],
    "title": "帝京篇十首 一"
  }
]
```

注意: 为了举例方便， 省略了剩下999篇诗.

### 作者JSON结构

*authors.tang.json*

*authors.song.json*

```json
[
  {
    "name": "太宗皇帝",
    "desc": "帝姓李氏，諱世民，神堯次子，聰明英武。貞觀之治，庶幾成康，功德兼隆。由漢以來，未之有也。而銳情經術，初建秦邸，即開文學館，召名儒十八人爲學士。既即位，殿左置弘文館，悉引內學士，番宿更休。聽朝之間，則與討論典籍，雜以文詠。或日昃夜艾，未嘗少怠。詩筆草隸，卓越前古。至於天文秀發，沈麗高朗，有唐三百年風雅之盛，帝實有以啓之焉。在位二十四年，諡曰文。集四十卷。館閣書目，詩一卷，六十九首。今編詩一卷。"
  }
]
```

注意: 为了举例方便， 仅举例一个诗人作者信息.
