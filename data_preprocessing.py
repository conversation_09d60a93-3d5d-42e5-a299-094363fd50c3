# -*- coding: utf-8 -*-
"""
中文古典诗歌数据预处理工具
Chinese Classical Poetry Data Preprocessing Tool

功能包括：
1. 数据清洗和标准化
2. 文本预处理
3. 数据统计分析
4. 格式转换
5. 数据验证
"""

import json
import os
import re
import jieba
import pandas as pd
from collections import Counter, defaultdict
from typing import List, Dict, Any, Tuple
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ChinesePoetryPreprocessor:
    """中文古典诗歌数据预处理器"""
    
    def __init__(self, data_root: str = "./"):
        """
        初始化预处理器
        
        Args:
            data_root: 数据根目录路径
        """
        self.data_root = Path(data_root)
        self.punctuation_pattern = re.compile(r'[，。、；：！？""''（）《》【】\s\n\r\t]')
        self.traditional_to_simplified = self._load_char_mapping()
        
    def _load_char_mapping(self) -> Dict[str, str]:
        """加载繁简字对照表"""
        # 这里可以扩展更完整的繁简对照表
        char_mapping = {
            "鵷": "鹓", "颭": "飐", "鷁": "鹢", "鴞": "鸮",
            "餖": "饾", "飣": "饤", "舃": "舄", "駸": "骎",
            "薄倖": "薄幸", "赬": "赪", "鷫鸘": "鹔鹴",
            "嶮": "崄", "後": "后", "纇": "颣", "颸": "飔",
            "崑崙": "昆仑", "曨": "昽"
        }
        return char_mapping
    
    def clean_text(self, text: str, remove_punctuation: bool = False) -> str:
        """
        清洗文本
        
        Args:
            text: 原始文本
            remove_punctuation: 是否移除标点符号
            
        Returns:
            清洗后的文本
        """
        if not text:
            return ""
            
        # 移除多余的空白字符
        text = re.sub(r'\s+', '', text)
        
        # 繁体转简体
        for traditional, simplified in self.traditional_to_simplified.items():
            text = text.replace(traditional, simplified)
        
        # 移除标点符号（可选）
        if remove_punctuation:
            text = self.punctuation_pattern.sub('', text)
            
        return text.strip()
    
    def load_poetry_data(self, file_path: str) -> List[Dict[str, Any]]:
        """
        加载诗歌数据
        
        Args:
            file_path: JSON文件路径
            
        Returns:
            诗歌数据列表
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"成功加载 {len(data)} 首诗歌从 {file_path}")
            return data
        except Exception as e:
            logger.error(f"加载文件失败 {file_path}: {e}")
            return []
    
    def preprocess_poetry_collection(self, collection_path: str) -> List[Dict[str, Any]]:
        """
        预处理整个诗歌集合
        
        Args:
            collection_path: 诗歌集合路径（文件或目录）
            
        Returns:
            预处理后的诗歌数据
        """
        processed_data = []
        path = Path(collection_path)
        
        if path.is_file() and path.suffix == '.json':
            # 单个文件
            data = self.load_poetry_data(str(path))
            processed_data.extend(self._process_poems(data))
        elif path.is_dir():
            # 目录中的所有JSON文件
            for json_file in path.glob('*.json'):
                if json_file.name not in ['README.md', 'authors.tang.json', 'authors.song.json']:
                    data = self.load_poetry_data(str(json_file))
                    processed_data.extend(self._process_poems(data))
        
        logger.info(f"预处理完成，共处理 {len(processed_data)} 首诗歌")
        return processed_data
    
    def _process_poems(self, poems: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        处理诗歌列表
        
        Args:
            poems: 原始诗歌数据
            
        Returns:
            处理后的诗歌数据
        """
        processed = []
        
        for poem in poems:
            try:
                processed_poem = self._process_single_poem(poem)
                if processed_poem:
                    processed.append(processed_poem)
            except Exception as e:
                logger.warning(f"处理诗歌时出错: {e}")
                continue
                
        return processed
    
    def _process_single_poem(self, poem: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理单首诗歌
        
        Args:
            poem: 单首诗歌数据
            
        Returns:
            处理后的诗歌数据
        """
        processed = {}
        
        # 基本信息
        processed['author'] = self.clean_text(poem.get('author', ''))
        processed['title'] = self.clean_text(poem.get('title', ''))
        
        # 处理诗歌内容
        paragraphs = poem.get('paragraphs', [])
        if isinstance(paragraphs, list):
            processed['paragraphs'] = [self.clean_text(p) for p in paragraphs if p.strip()]
            processed['content'] = ''.join(processed['paragraphs'])
            processed['content_no_punct'] = self.clean_text(processed['content'], remove_punctuation=True)
        else:
            processed['paragraphs'] = []
            processed['content'] = ''
            processed['content_no_punct'] = ''
        
        # 词牌名（宋词特有）
        if 'rhythmic' in poem:
            processed['rhythmic'] = self.clean_text(poem['rhythmic'])
        
        # 统计信息
        processed['char_count'] = len(processed['content_no_punct'])
        processed['line_count'] = len(processed['paragraphs'])
        
        # 保留原始ID
        if 'id' in poem:
            processed['id'] = poem['id']
            
        return processed

    def analyze_poetry_statistics(self, poems: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        分析诗歌统计信息

        Args:
            poems: 诗歌数据列表

        Returns:
            统计分析结果
        """
        stats = {
            'total_poems': len(poems),
            'total_authors': len(set(p['author'] for p in poems if p['author'])),
            'char_count_distribution': [],
            'line_count_distribution': [],
            'top_authors': [],
            'top_words': [],
            'rhythmic_distribution': []  # 词牌名分布（宋词）
        }

        # 字数和行数分布
        char_counts = [p['char_count'] for p in poems]
        line_counts = [p['line_count'] for p in poems]

        stats['char_count_distribution'] = {
            'mean': sum(char_counts) / len(char_counts) if char_counts else 0,
            'min': min(char_counts) if char_counts else 0,
            'max': max(char_counts) if char_counts else 0,
            'distribution': Counter(char_counts)
        }

        stats['line_count_distribution'] = {
            'mean': sum(line_counts) / len(line_counts) if line_counts else 0,
            'min': min(line_counts) if line_counts else 0,
            'max': max(line_counts) if line_counts else 0,
            'distribution': Counter(line_counts)
        }

        # 作者作品数量排行
        author_counts = Counter(p['author'] for p in poems if p['author'])
        stats['top_authors'] = author_counts.most_common(20)

        # 高频词分析
        all_text = ''.join(p['content_no_punct'] for p in poems)
        words = list(jieba.cut(all_text))
        word_counts = Counter(w for w in words if len(w) > 1)
        stats['top_words'] = word_counts.most_common(50)

        # 词牌名分布（如果有）
        rhythmic_counts = Counter(p.get('rhythmic', '') for p in poems if p.get('rhythmic'))
        if rhythmic_counts:
            stats['rhythmic_distribution'] = rhythmic_counts.most_common(20)

        return stats

    def validate_data_quality(self, poems: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        验证数据质量

        Args:
            poems: 诗歌数据列表

        Returns:
            数据质量报告
        """
        quality_report = {
            'total_poems': len(poems),
            'valid_poems': 0,
            'issues': {
                'missing_author': 0,
                'missing_content': 0,
                'empty_paragraphs': 0,
                'duplicate_content': 0,
                'encoding_issues': 0
            },
            'duplicate_poems': []
        }

        content_seen = set()

        for i, poem in enumerate(poems):
            is_valid = True

            # 检查作者
            if not poem.get('author', '').strip():
                quality_report['issues']['missing_author'] += 1
                is_valid = False

            # 检查内容
            if not poem.get('content', '').strip():
                quality_report['issues']['missing_content'] += 1
                is_valid = False

            # 检查段落
            if not poem.get('paragraphs') or len(poem['paragraphs']) == 0:
                quality_report['issues']['empty_paragraphs'] += 1
                is_valid = False

            # 检查重复内容
            content_key = poem.get('content_no_punct', '')
            if content_key and content_key in content_seen:
                quality_report['issues']['duplicate_content'] += 1
                quality_report['duplicate_poems'].append({
                    'index': i,
                    'author': poem.get('author'),
                    'title': poem.get('title'),
                    'content': poem.get('content', '')[:50] + '...'
                })
                is_valid = False
            else:
                content_seen.add(content_key)

            # 检查编码问题
            try:
                poem.get('content', '').encode('utf-8')
            except UnicodeEncodeError:
                quality_report['issues']['encoding_issues'] += 1
                is_valid = False

            if is_valid:
                quality_report['valid_poems'] += 1

        quality_report['quality_score'] = (
            quality_report['valid_poems'] / quality_report['total_poems'] * 100
            if quality_report['total_poems'] > 0 else 0
        )

        return quality_report

    def export_to_formats(self, poems: List[Dict[str, Any]], output_dir: str = "./output"):
        """
        导出数据到不同格式

        Args:
            poems: 诗歌数据列表
            output_dir: 输出目录
        """
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        # 导出为JSON
        json_file = output_path / "processed_poetry.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(poems, f, ensure_ascii=False, indent=2)
        logger.info(f"导出JSON文件: {json_file}")

        # 导出为CSV
        csv_file = output_path / "processed_poetry.csv"
        df = pd.DataFrame(poems)
        df.to_csv(csv_file, index=False, encoding='utf-8')
        logger.info(f"导出CSV文件: {csv_file}")

        # 导出纯文本（用于训练语言模型）
        txt_file = output_path / "poetry_corpus.txt"
        with open(txt_file, 'w', encoding='utf-8') as f:
            for poem in poems:
                if poem.get('content'):
                    f.write(f"{poem['content']}\n")
        logger.info(f"导出文本语料: {txt_file}")

    def create_author_index(self, poems: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        创建作者索引

        Args:
            poems: 诗歌数据列表

        Returns:
            按作者分组的诗歌索引
        """
        author_index = defaultdict(list)

        for poem in poems:
            author = poem.get('author', '未知')
            author_index[author].append(poem)

        # 按作品数量排序
        sorted_authors = dict(sorted(
            author_index.items(),
            key=lambda x: len(x[1]),
            reverse=True
        ))

        return sorted_authors

    def filter_poems(self, poems: List[Dict[str, Any]], **filters) -> List[Dict[str, Any]]:
        """
        根据条件过滤诗歌

        Args:
            poems: 诗歌数据列表
            **filters: 过滤条件
                - author: 作者名称
                - min_chars: 最小字数
                - max_chars: 最大字数
                - min_lines: 最小行数
                - max_lines: 最大行数
                - rhythmic: 词牌名（宋词）

        Returns:
            过滤后的诗歌列表
        """
        filtered = poems

        if 'author' in filters:
            author = filters['author']
            filtered = [p for p in filtered if p.get('author') == author]

        if 'min_chars' in filters:
            min_chars = filters['min_chars']
            filtered = [p for p in filtered if p.get('char_count', 0) >= min_chars]

        if 'max_chars' in filters:
            max_chars = filters['max_chars']
            filtered = [p for p in filtered if p.get('char_count', 0) <= max_chars]

        if 'min_lines' in filters:
            min_lines = filters['min_lines']
            filtered = [p for p in filtered if p.get('line_count', 0) >= min_lines]

        if 'max_lines' in filters:
            max_lines = filters['max_lines']
            filtered = [p for p in filtered if p.get('line_count', 0) <= max_lines]

        if 'rhythmic' in filters:
            rhythmic = filters['rhythmic']
            filtered = [p for p in filtered if p.get('rhythmic') == rhythmic]

        logger.info(f"过滤后剩余 {len(filtered)} 首诗歌")
        return filtered


def main():
    """主函数 - 演示数据预处理流程"""

    # 初始化预处理器
    preprocessor = ChinesePoetryPreprocessor()

    # 处理唐诗数据（示例）
    print("=== 处理唐诗数据 ===")
    tang_poems = preprocessor.preprocess_poetry_collection("全唐诗/poet.tang.0.json")

    if tang_poems:
        # 统计分析
        print("\n=== 统计分析 ===")
        stats = preprocessor.analyze_poetry_statistics(tang_poems[:1000])  # 取前1000首进行分析
        print(f"总诗歌数: {stats['total_poems']}")
        print(f"总作者数: {stats['total_authors']}")
        print(f"平均字数: {stats['char_count_distribution']['mean']:.1f}")
        print(f"平均行数: {stats['line_count_distribution']['mean']:.1f}")

        print("\n前10位高产作者:")
        for author, count in stats['top_authors'][:10]:
            print(f"  {author}: {count}首")

        print("\n前10个高频词:")
        for word, count in stats['top_words'][:10]:
            print(f"  {word}: {count}次")

        # 数据质量验证
        print("\n=== 数据质量验证 ===")
        quality = preprocessor.validate_data_quality(tang_poems[:1000])
        print(f"数据质量评分: {quality['quality_score']:.1f}%")
        print(f"有效诗歌: {quality['valid_poems']}/{quality['total_poems']}")

        for issue, count in quality['issues'].items():
            if count > 0:
                print(f"  {issue}: {count}")

        # 过滤示例
        print("\n=== 过滤示例 ===")
        filtered = preprocessor.filter_poems(
            tang_poems,
            author="李白",
            min_chars=20,
            max_chars=100
        )
        print(f"李白的20-100字诗歌: {len(filtered)}首")

        # 导出数据
        print("\n=== 导出数据 ===")
        preprocessor.export_to_formats(tang_poems[:100], "./output")  # 导出前100首作为示例


if __name__ == "__main__":
    main()
